cc.Class({
    extends: cc.Component,

    properties: {
        button: cc.Node
    },

    // LIFE-CYCLE CALLBACKS:

     onLoad () {
        this.button.on('click', this.onStartBtn);
        let zhangsan = {
            name: "张三"
        }
        let arr = [2001,2002,2003];
        this.test.call(zhang<PERSON>,1,2,3);
     },



    // update (dt) {},

    // 添加一个函数，用于处理按钮点击之后所要做的事情
    onStartBtn(event) {
        // console.log("点击按钮了！");
        console.log(event);
        // console.log(parseInt(id));
        // string类型转换成number
        // parseInt();
        // 进入到游戏场景
        // cc.director.loadScene("game");
    },

    test(id1,id2,id3){
        console.log("调用了test函数了！");
        console.log(id1);
        console.log(id2);
        console.log(id3);
    }
});
